import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;
import '/src/domain/models/filter/table_filter.dart';
import '../../../../core/utils/callback_functions.dart';
import '/src/core/config/app_strings.dart';
import '/src/core/config/constants.dart';
import '/src/core/config/responsive.dart';
import '/src/core/theme/app_theme.dart';
import 'dart:math' as math;
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/utils/date_formatter.dart';
import '../calendar/custom_calendar_widget.dart';
import '../calendar/date_range_utils.dart';

class TableCellData {
  final String text;
  final Widget? widget;
  final TextAlign? alignment;
  final IconData? leftIcon;
  final IconData? rightIcon;
  final String? leftIconAsset;
  final String? rightIconAsset;
  final Color? iconColor;
  final double? iconSize;

  TableCellData({
    required this.text,
    this.widget,
    this.alignment,
    this.leftIcon,
    this.rightIcon,
    this.leftIconAsset,
    this.rightIconAsset,
    this.iconColor,
    this.iconSize = 16,
  });
}

class CustomDataTableWidget<T> extends HookWidget {
  /// Map of filter column name to bool: true if onChange listener should be enabled for that filter
  final Map<String, bool>? filterOnChangeConfig;

  /// Callback for filter onChange: (filterKey, selectedValue, clearDependentFilters)
  final Future<void> Function(
    String filterKey,
    String? selectedValue, [
    void Function(List<String>)? clearDependentFilters,
  ])?
  onFilterChange;

  /// Map of filter column name to list of filter model (dynamic)
  final Map<String, List<TableFilter>>? filterOptions;

  /// Map of filter column name to value extractor for dropdown display
  final Map<String, String Function(dynamic)>? filterOptionValueExtractors;
  final List<T> data;
  final String? title;
  final String? titleIcon;
  final String? searchHint;
  final String? titleFilterLabel;

  // Legacy filter system (for backward compatibility)
  final List<String> filter1Options;
  final List<String> filter2Options;
  final List<String> filter3Options;
  final String? filter1Label;
  final String? filter2Label;
  final String? filter3Label;
  final bool Function(T item, String?, String?, String?)? filterFn;

  // Dynamic filter configuration (new system)
  final List<String> filterColumnNames; // Column names to create filters for
  final Map<String, String Function(T)>?
  filterValueExtractors; // How to extract values for each filter column
  final List<String>
  dateFilterColumns; // Columns that should use date filters instead of dropdowns

  final String Function(T item)? searchFn;
  final List<String> columnNames;
  final List<String Function(T)> cellBuilders;
  final List<Widget Function(BuildContext, T)?>?
  widgetCellBuilders; // Optional widget builders for specific columns (can contain null)
  final List<bool>?
  useWidgetBuilders; // Boolean flags to indicate which columns should use widget builders
  final List<Widget Function(BuildContext, T)>? actionBuilders;
  final Widget Function(BuildContext, T)? mobileCardBuilder;

  // New properties for icon support
  final List<TableCellData Function(T)?>?
  iconCellBuilders; // Returns TableCellData with icon info
  final List<bool>?
  useIconBuilders; // Boolean flags for which columns use icon builders

  // Empty state customization
  final String? emptyStateMessage;

  // Min height constraint
  final bool useMinHeight;
  final double? minHeight;
  final bool useExpandedView;

  //TODO: Change later
  final bool isLoading;
  final int pageCount;
  final int totalElements;

  final void Function(String columnName, bool ascending)? onSort;
  final DateTimeCallback? onDateFilterChanged;
  final DateFilterMultipleCallback? onDateFilterMultipleChanged;
  final StringCallback? handleTableSearch;
  final IntCallback? handlePagination;
  final StringCallback? handleBrokerNameFilter;
  final AllFiltersCallback? onAllFiltersChanged;

  // Static variables to track filter positions for calendar positioning
  static final Map<String, Offset> _filterPositions = {};
  static String? _activeFilterColumn;

  // Static variable to store applied filter options snapshot
  static Map<String, List<dynamic>>? _appliedFilterOptionsSnapshot;

  final List<String>? showSortIconColumns;

  const CustomDataTableWidget({
    super.key,
    required this.data,
    this.title,
    this.titleIcon,
    this.searchHint,
    this.searchFn,
    required this.columnNames,
    required this.cellBuilders,
    this.widgetCellBuilders,
    this.useWidgetBuilders,
    this.iconCellBuilders,
    this.useIconBuilders,
    this.actionBuilders,
    this.mobileCardBuilder,
    this.onSort,
    this.titleFilterLabel,
    // Legacy filter system
    this.filter1Options = const [],
    this.filter2Options = const [],
    this.filter3Options = const [],
    this.filter1Label,
    this.filter2Label,
    this.filter3Label,
    this.filterFn,
    // Dynamic filter system
    this.filterColumnNames = const [],
    this.filterValueExtractors,
    this.dateFilterColumns = const [],
    this.emptyStateMessage,
    // Min height constraint
    this.useMinHeight = false,
    this.isLoading = false,
    this.minHeight,
    this.pageCount = 0,
    this.totalElements = 0,
    this.onDateFilterChanged,
    this.onDateFilterMultipleChanged,
    this.handleBrokerNameFilter,
    this.handleTableSearch,
    this.handlePagination,
    this.useExpandedView = false,
    this.filterOptions,
    this.filterOptionValueExtractors,
    this.onAllFiltersChanged,
    this.filterOnChangeConfig,
    this.onFilterChange,
    this.showSortIconColumns,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure initialFilterOptions is defined before use.
    final initialFilterOptionsRef = useRef<Map<String, List<String>>?>(null);
    if (initialFilterOptionsRef.value == null && data.isNotEmpty) {
      final Map<String, List<String>> options = {};
      for (final column in filterColumnNames) {
        final extractor =
            filterValueExtractors != null &&
                filterValueExtractors!.containsKey(column)
            ? filterValueExtractors![column]
            : null;
        if (extractor != null) {
          final set = data.map((item) => extractor(item)).toSet();
          final list = set.toList();
          list.sort();
          options[column] = list;
        }
      }
      initialFilterOptionsRef.value = options;
    }
    final initialFilterOptions = initialFilterOptionsRef.value ?? {};
    final currentPage = useState(1);
    final itemsPerPage = useState(10);
    final selectedF1 = useState<String?>(null);
    final selectedF2 = useState<String?>(null);
    final selectedF3 = useState<String?>(null);
    final searchQuery = useState('');
    final showFilter = useState(false);
    final showTooltip = useState(false);
    final appliedF1 = useState<String?>(null);
    final appliedF2 = useState<String?>(null);
    final appliedF3 = useState<String?>(null);
    final sortColumn = useState<String>('');
    final sortAscending = useState<bool>(true);

    // Dynamic filter states
    final appliedFilters = useState<Map<String, String?>>({});
    final selectedFilters = useState<Map<String, String?>>({});

    // Calendar states - FORCE INITIALIZE TO FALSE
    final showCalendar = useState<bool>(false);
    final activeDateColumn = useState<String?>(
      null,
    ); // Track which date column is being filtered
    final selectedQuickOption = useState('');
    final selectedDate = useState<DateTime?>(null);
    final rangeStart = useState<DateTime?>(null);
    final rangeEnd = useState<DateTime?>(null);
    final isCustomRangeMode = useState(false);
    final customRangeStart = useState<DateTime?>(null);
    final customRangeEnd = useState<DateTime?>(null);
    final serverFilterDate = useState<DateTime?>(null);

    final currentMonth = useState(DateTime.now());

    // Track if calendar was explicitly opened by user interaction
    final calendarExplicitlyOpened = useState<bool>(false);
    final selectedDateFilters = useState<Map<String, DateTime?>>({});

    useEffect(() {
      if (!showFilter.value && !showTooltip.value) {
        _clearAllAppliedSelectedFilters(
          appliedF1,
          appliedF2,
          appliedF3,
          appliedFilters,
          serverFilterDate,
          selectedDate,
          selectedF1,
          selectedF2,
          selectedF3,
          selectedFilters,
          selectedDateFilters,
        );
      }
      return null;
    }, [showFilter.value, showTooltip.value]);

    // Update explicit flag when calendar is shown
    useEffect(() {
      if (showCalendar.value) {
        calendarExplicitlyOpened.value = true;
      } else {
        calendarExplicitlyOpened.value = false;
      }
      return null;
    }, [showCalendar.value]);

    void handleSort(String columnName) {
      if (onSort != null) {
        if (sortColumn.value == columnName) {
          sortAscending.value = !sortAscending.value;
        } else {
          sortColumn.value = columnName;
          sortAscending.value = true;
        }
        onSort!(columnName, sortAscending.value);
      }
    }

    List<T> getFilteredItems() {
      return data;
    }

    List<T> getPaginatedItems() {
      final filtered = data;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Safety check: if current page is beyond available pages, reset to page 1
        final totalPages =
            pageCount; // (filtered.length / itemsPerPage.value).ceil();
        if (currentPage.value > totalPages && totalPages > 0) {
          currentPage.value = 1;
        }
      });
      return filtered;
    }

    // Map to track selected date for each date filter column

    return Material(
      color: Colors.transparent, // Make root Material transparent
      child: Stack(
        clipBehavior: Clip.none, // Allow overflow
        children: [
          LayoutBuilder(
            builder: (context, constraints) {
              return Container(
                width: constraints.maxWidth,
                constraints: useMinHeight && minHeight != null
                    ? BoxConstraints(minHeight: minHeight!)
                    : BoxConstraints(
                        minHeight: constraints.maxHeight > 500
                            ? 300
                            : constraints.maxHeight,
                      ),
                padding: const EdgeInsets.only(bottom: defaultPadding - 2),
                decoration: BoxDecoration(
                  color: AppTheme.white, // Make outer container transparent
                  borderRadius: BorderRadius.circular(15),
                  // Remove shadow from outer container since inner container will handle styling
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Use AnimatedBuilder to listen to multiple ValueNotifiers for filter widget updates
                      AnimatedBuilder(
                        animation: Listenable.merge([
                          showFilter,
                          selectedF1,
                          selectedF2,
                          selectedF3,
                          selectedDateFilters,
                        ]),
                        builder: (context, child) {
                          if (!showFilter.value) return const SizedBox.shrink();

                          return _buildFilterWidget(
                            context,
                            selectedF1,
                            selectedF2,
                            selectedF3,
                            showFilter,
                            currentPage,
                            showTooltip,
                            appliedF1,
                            appliedF2,
                            appliedF3,
                            appliedFilters,
                            selectedFilters,
                            showCalendar,
                            activeDateColumn,
                            // Calendar state variables
                            selectedQuickOption,
                            selectedDate,
                            rangeStart,
                            rangeEnd,
                            isCustomRangeMode,
                            customRangeStart,
                            customRangeEnd,
                            currentMonth,
                            serverFilterDate,
                            initialFilterOptions,
                            selectedDateFilters, // pass the state to the filter widget
                          );
                        },
                      ),
                      // Only show header if title is provided or filters/search are available
                      if (title != null ||
                          filterColumnNames.isNotEmpty ||
                          filter1Options.isNotEmpty ||
                          filter2Options.isNotEmpty ||
                          filter3Options.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.fromLTRB(
                            defaultPadding * 1.5,
                            defaultPadding - 2,
                            defaultPadding * 1.5,
                            0,
                          ),
                          child: _buildHeader(
                            context,
                            searchQuery,
                            showFilter,
                            showTooltip,
                            appliedF1,
                            appliedF2,
                            appliedF3,
                            appliedFilters,
                            selectedF1,
                            selectedF2,
                            selectedF3,
                            selectedFilters,
                            serverFilterDate,
                            onDateFilterChanged,
                          ),
                        ),
                      if (title != null ||
                          filterColumnNames.isNotEmpty ||
                          filter1Options.isNotEmpty ||
                          filter2Options.isNotEmpty ||
                          filter3Options.isNotEmpty)
                        const SizedBox(height: 4),

                      // Table content (no Flexible/Expanded)
                      useExpandedView
                          ? Expanded(
                              child: _buildTableContent(
                                context,
                                getPaginatedItems,
                                handleSort,
                                sortColumn,
                                sortAscending,
                              ),
                            )
                          : Flexible(
                              child: _buildTableContent(
                                context,
                                getPaginatedItems,
                                handleSort,
                                sortColumn,
                                sortAscending,
                              ),
                            ),
                      Padding(
                        padding: const EdgeInsets.fromLTRB(
                          defaultPadding * 1.5,
                          defaultPadding,
                          defaultPadding * 1.5,
                          defaultPadding * 1.5,
                        ),
                        child: _buildPagination(
                          context,
                          getFilteredItems(),
                          currentPage,
                          itemsPerPage,
                          handlePagination,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          // Calendar popup overlay
          if (showCalendar.value) ...[
            // Barrier to close calendar - positioned to cover entire screen
            Positioned(
              top: -1000, // Large negative values to ensure full coverage
              left: -1000,
              right: -1000,
              bottom: -1000,
              child: GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  showCalendar.value = false;
                  calendarExplicitlyOpened.value = false;
                },
                child: Container(color: Colors.transparent),
              ),
            ),

            // Calendar positioned above the barrier
            Positioned(
              top: _calculateCalendarTop(context, showFilter),
              left: _calculateCalendarPosition(context, showFilter)['left'],
              right: _calculateCalendarPosition(context, showFilter)['right'],
              child: CustomCalendarWidget(
                currentMonth: currentMonth.value,
                selectedQuickOption: selectedQuickOption.value,
                selectedDate: selectedDate.value,
                rangeStart: rangeStart.value,
                rangeEnd: rangeEnd.value,
                isCustomRangeMode: isCustomRangeMode.value,
                customRangeStart: customRangeStart.value,
                customRangeEnd: customRangeEnd.value,
                fromFilter: true,
                onQuickSelection: (option) {
                  selectedQuickOption.value = option;
                  if (option == 'Custom Range') {
                    isCustomRangeMode.value = true;
                    // Clear all date selections for a clean custom range experience
                    customRangeStart.value = null;
                    customRangeEnd.value = null;
                    selectedDate.value = null;
                    rangeStart.value = null;
                    rangeEnd.value = null;
                  } else {
                    isCustomRangeMode.value = false;
                    // Clear custom range values when selecting other options
                    customRangeStart.value = null;
                    customRangeEnd.value = null;
                    // Set the quick selection range
                    final range = DateRangeUtils.getQuickSelectionRange(option);
                    rangeStart.value = range['start'];
                    rangeEnd.value = range['end'];
                    selectedDate.value = null;
                  }
                },
                onDateSelection: (date) {
                  if (isCustomRangeMode.value) {
                    if (customRangeStart.value == null) {
                      // First date selection
                      customRangeStart.value = date;
                    } else if (customRangeEnd.value == null) {
                      // Second date selection - auto-arrange start and end
                      if (date.isBefore(customRangeStart.value!)) {
                        customRangeEnd.value = customRangeStart.value;
                        customRangeStart.value = date;
                      } else {
                        customRangeEnd.value = date;
                      }
                    } else {
                      // Both dates already selected - intelligent range adjustment
                      final currentStart = customRangeStart.value!;
                      final currentEnd = customRangeEnd.value!;

                      if (date.isBefore(currentStart)) {
                        // New date is before current range - make it the new start
                        customRangeStart.value = date;
                      } else if (date.isAfter(currentEnd)) {
                        // New date is after current range - make it the new end
                        customRangeEnd.value = date;
                      } else {
                        // New date is within current range - replace the closer end
                        final daysFromStart = date
                            .difference(currentStart)
                            .inDays
                            .abs();
                        final daysFromEnd = date
                            .difference(currentEnd)
                            .inDays
                            .abs();
                        if (daysFromStart <= daysFromEnd) {
                          // Closer to start - replace start
                          customRangeStart.value = date;
                        } else {
                          // Closer to end - replace end
                          customRangeEnd.value = date;
                        }
                      }
                    }
                  } else {
                    selectedDate.value = date;
                    rangeStart.value = null;
                    rangeEnd.value = null;
                    selectedQuickOption.value = '';
                  }
                },
                onNavigateMonth: (monthOffset) {
                  currentMonth.value = DateTime(
                    currentMonth.value.year,
                    currentMonth.value.month + monthOffset,
                  );
                },
                onCancel: () {
                  showCalendar.value = false;
                  _activeFilterColumn = null; // Clear active filter position
                },
                onApply: () {
                  showCalendar.value = false;
                  _activeFilterColumn = null; // Clear active filter position
                  // Get the active date column name
                  final columnName = activeDateColumn.value;
                  if (columnName != null) {
                    String? filterValue;
                    if (selectedDate.value != null) {
                      filterValue = AppDateFormatter.formatDateMMddyyyy(
                        selectedDate.value!,
                      );
                      serverFilterDate.value = selectedDate.value;
                    } else if (isCustomRangeMode.value &&
                        customRangeStart.value != null &&
                        customRangeEnd.value != null) {
                      filterValue = AppDateFormatter.formatDateMMddyyyy(
                        customRangeStart.value!,
                      );
                      serverFilterDate.value = customRangeStart.value;
                    } else if (rangeStart.value != null &&
                        rangeEnd.value != null) {
                      filterValue = AppDateFormatter.formatDateMMddyyyy(
                        rangeStart.value!,
                      );
                      serverFilterDate.value = rangeStart.value;
                    }
                    // Update selectedFilters for the date column
                    final newSelectedFilters = Map<String, String?>.from(
                      selectedFilters.value,
                    );
                    if (filterValue != null) {
                      newSelectedFilters[columnName] = filterValue;
                    } else {
                      newSelectedFilters.remove(columnName);
                    }
                    selectedFilters.value = newSelectedFilters;
                    // Assign to selectedDateFilters:
                    final newMap = Map<String, DateTime?>.from(
                      selectedDateFilters.value,
                    );
                    newMap[columnName] = isCustomRangeMode.value
                        ? customRangeStart
                              .value // or customRangeEnd.value for range end
                        : selectedDate.value;
                    selectedDateFilters.value = newMap;
                  }
                  // Do NOT update appliedFilters here!
                },
                isDateInSelectedRange: (date) {
                  if (isCustomRangeMode.value) {
                    return DateRangeUtils.isDateInRange(
                      date,
                      customRangeStart.value,
                      customRangeEnd.value,
                    );
                  }
                  return DateRangeUtils.isDateInRange(
                    date,
                    rangeStart.value,
                    rangeEnd.value,
                  );
                },
                isRangeStartDate: (date) {
                  if (isCustomRangeMode.value) {
                    return DateRangeUtils.isRangeStart(
                      date,
                      customRangeStart.value,
                    );
                  }
                  return DateRangeUtils.isRangeStart(date, rangeStart.value);
                },
              ),
            ),
          ],
        ], // Close Stack children array
      ), // Close Stack
    ); // Close Material
  }

  Widget _buildTableContent(
    BuildContext context,
    List<T> getPaginatedItems(),
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return isLoading
        ? Container(
            padding: EdgeInsets.symmetric(
              horizontal: defaultPadding * 1.5,
              vertical: defaultPadding,
            ),
            child: Center(child: CircularProgressIndicator()),
          )
        : SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: defaultPadding * 1.5,
              ),
              child: _buildTable(
                context,
                getPaginatedItems(),
                handleSort,
                sortColumn,
                sortAscending,
              ),
            ),
          );
  }

  Widget _buildFilterWidget(
    BuildContext context,
    ValueNotifier<String?> selectedFilter1,
    ValueNotifier<String?> selectedFilter2,
    ValueNotifier<String?> selectedFilter3,
    ValueNotifier<bool> showFilter,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedFilter1,
    ValueNotifier<String?> appliedFilter2,
    ValueNotifier<String?> appliedFilter3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<bool> showCalendar,
    ValueNotifier<String?> activeDateColumn,
    // Calendar state variables for reset
    ValueNotifier<String> selectedQuickOption,
    ValueNotifier<DateTime?> selectedDate,
    ValueNotifier<DateTime?> rangeStart,
    ValueNotifier<DateTime?> rangeEnd,
    ValueNotifier<bool> isCustomRangeMode,
    ValueNotifier<DateTime?> customRangeStart,
    ValueNotifier<DateTime?> customRangeEnd,
    ValueNotifier<DateTime> currentMonth,
    ValueNotifier<DateTime?> filterDate,
    Map<String, List<String>> initialFilterOptions,
    ValueNotifier<Map<String, DateTime?>> selectedDateFilters,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: AppTheme.filterBgColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(filterBy, style: AppFonts.semiBoldTextStyle(18)),
              Tooltip(
                message: AppStrings.clearFilters,
                child: InkWell(
                  mouseCursor: SystemMouseCursors.click,
                  onTap: () {
                    showFilter.value = false;
                    selectedFilter1.value = null;
                    selectedFilter2.value = null;
                    selectedFilter3.value = null;
                    selectedDateFilters.value = {};
                    selectedFilters.value = {};
                  },
                  child: const Icon(Icons.close, size: 20),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Make filter controls responsive with proper wrapping
          LayoutBuilder(
            builder: (context, constraints) {
              final isSmallScreen = constraints.maxWidth < 600;

              // Build dynamic filters
              List<Widget> filterWidgets = [];

              // Add legacy filters if they exist
              if (filter1Options.isNotEmpty && filter1Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter1Label!,
                    selectedFilter1.value,
                    filter1Options,
                    (value) => selectedFilter1.value = value,
                  ),
                );
              }
              if (filter2Options.isNotEmpty && filter2Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter2Label!,
                    selectedFilter2.value,
                    filter2Options,
                    (value) => selectedFilter2.value = value,
                  ),
                );
              }
              if (filter3Options.isNotEmpty && filter3Label != null) {
                filterWidgets.add(
                  _buildDropdown(
                    filter3Label!,
                    selectedFilter3.value,
                    filter3Options,
                    (value) => selectedFilter3.value = value,
                  ),
                );
              }

              // Add dynamic filters
              for (String columnName in filterColumnNames) {
                if (dateFilterColumns.contains(columnName)) {
                  filterWidgets.add(
                    _buildDateFilter(
                      context,
                      _getFilterLabel(columnName),
                      selectedFilters.value[columnName],
                      (value) {
                        final newFilters = Map<String, String?>.from(
                          selectedFilters.value,
                        );
                        newFilters[columnName] = value;
                        selectedFilters.value = newFilters;
                      },
                      showCalendar,
                      columnName,
                      activeDateColumn,
                      selectedQuickOption,
                      selectedDate,
                      rangeStart,
                      rangeEnd,
                      isCustomRangeMode,
                      customRangeStart,
                      customRangeEnd,
                      currentMonth,
                      appliedFilters,
                      selectedFilters,
                    ),
                  );
                } else {
                  // Use filterOptions if provided, else fallback to initialFilterOptions
                  final List<TableFilter> optionsList =
                      filterOptions != null &&
                          filterOptions![columnName] != null
                      ? filterOptions![columnName]!
                      : [];
                  //debugPrint('Dropdown for $columnName options: ' + optionsList.toString());
                  String? selectedId = selectedFilters.value[columnName];

                  final valueExtractor = filterOptionValueExtractors != null
                      ? filterOptionValueExtractors![columnName]
                      : null;
                  filterWidgets.add(
                    _buildDropdownWithModel(
                      _getFilterLabel(columnName),
                      selectedId,
                      optionsList,
                      valueExtractor,
                      (value) {
                        final newFilters = Map<String, String?>.from(
                          selectedFilters.value,
                        );
                        newFilters[columnName] = value == 'Select'
                            ? null
                            : value;
                        selectedFilters.value = newFilters;
                        // If onChange is enabled for this filter, call the callback
                        if (filterOnChangeConfig != null &&
                            filterOnChangeConfig![columnName] == true &&
                            onFilterChange != null) {
                          // Create clearDependentFilters function
                          void clearDependentFilters(List<String> filterKeys) {
                            final newFilters = Map<String, String?>.from(
                              selectedFilters.value,
                            );
                            for (String key in filterKeys) {
                              newFilters[key] = null;
                            }
                            selectedFilters.value = newFilters;
                          }

                          onFilterChange!(
                            columnName,
                            value == 'Select' ? null : value,
                            clearDependentFilters,
                          );
                        }
                      },
                    ),
                  );
                }
              }

              if (isSmallScreen) {
                // Stack filters vertically on small screens
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (int i = 0; i < filterWidgets.length; i++) ...[
                      SizedBox(width: double.infinity, child: filterWidgets[i]),
                      if (i < filterWidgets.length - 1)
                        const SizedBox(height: 12),
                    ],
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      height: 40,
                      child: _buildFilterApplyBtn(
                        appliedFilter1,
                        selectedFilter1,
                        appliedFilter2,
                        selectedFilter2,
                        appliedFilter3,
                        selectedFilter3,
                        appliedFilters,
                        selectedFilters,
                        currentPage,
                        showTooltip,
                        filterDate,
                        selectedDateFilters: selectedDateFilters.value,
                      ),
                    ),
                  ],
                );
              } else {
                // Use horizontal layout for larger screens with proper wrapping
                return Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    // Add all filter widgets (legacy + dynamic)
                    for (Widget filterWidget in filterWidgets)
                      SizedBox(
                        width: ResponsiveSizes.comboBoxWidth(context),
                        child: filterWidget,
                      ),
                    SizedBox(
                      width: ResponsiveSizes.applyButtonWidth(context),
                      height: 40,
                      child: _buildFilterApplyBtn(
                        appliedFilter1,
                        selectedFilter1,
                        appliedFilter2,
                        selectedFilter2,
                        appliedFilter3,
                        selectedFilter3,
                        appliedFilters,
                        selectedFilters,
                        currentPage,
                        showTooltip,
                        filterDate,
                        selectedDateFilters: selectedDateFilters.value,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }

  /// Build a dropdown for a filter column with dynamic model and value extractor
  Widget _buildDropdownWithModel(
    String hint,
    String? selectedId,
    List<TableFilter> optionsList,
    String Function(dynamic)? valueExtractor,
    Function(String?) onChanged,
  ) {
    // If optionsList is empty, show 'Select' only
    if (optionsList.isEmpty) {
      return _buildDropdown(hint, null, ['Select'], onChanged);
    }
    return Tooltip(
      message: hint,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                selectedId != null && optionsList.any((e) => e.id == selectedId)
                ? AppTheme.selectedComboBoxBorder
                : AppTheme.comboBoxBorder,
            width: 1.0,
          ),
        ),
        child: DropdownButtonHideUnderline(
          child: ButtonTheme(
            alignedDropdown: true,
            child: DropdownButton<String>(
              value:
                  selectedId != null &&
                      optionsList.any((e) => e.id == selectedId)
                  ? selectedId
                  : null,
              hint: Padding(
                padding: const EdgeInsets.only(left: 12),
                child: Text(
                  hint,
                  style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                ),
              ),
              isExpanded: true,
              dropdownColor: Colors.white,
              menuMaxHeight: 300,
              borderRadius: BorderRadius.circular(20),
              items: optionsList.map((e) {
                final id = e.id?.toString() ?? '';
                final value = valueExtractor != null
                    ? valueExtractor(e)
                    : (e.value?.toString() ?? '');
                return DropdownMenuItem<String>(
                  value: id,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: Text(value, style: AppFonts.regularTextStyle(14)),
                  ),
                );
              }).toList(),
              onChanged: onChanged,
            ),
          ),
        ),
      ),
    );
  }

  ElevatedButton _buildFilterApplyBtn(
    ValueNotifier<String?> appliedFilter1,
    ValueNotifier<String?> selectedFilter1,
    ValueNotifier<String?> appliedFilter2,
    ValueNotifier<String?> selectedFilter2,
    ValueNotifier<String?> appliedFilter3,
    ValueNotifier<String?> selectedFilter3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<int> currentPage,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<DateTime?> filterDate, {
    Map<String, DateTime?>? selectedDateFilters,
  }) {
    // Check if there are any filters selected to apply
    // bool hasFiltersToApply() {
    //   // Check legacy filters
    //   if (selectedFilter1.value != null && selectedFilter1.value!.isNotEmpty)
    //     return true;
    //   if (selectedFilter2.value != null && selectedFilter2.value!.isNotEmpty)
    //     return true;
    //   if (selectedFilter3.value != null && selectedFilter3.value!.isNotEmpty)
    //     return true;

    //   // Check dynamic filters
    //   for (String? value in selectedFilters.value.values) {
    //     if (value != null && value.isNotEmpty) return true;
    //   }

    //   // Check date filters
    //   if (filterDate.value != null) return true;
    //   if (selectedDateFilters != null) {
    //     for (DateTime? date in selectedDateFilters.values) {
    //       if (date != null) return true;
    //     }
    //   }

    //   return false;
    // }

    return ElevatedButton(
      onPressed: () {
        // Apply legacy filters
        appliedFilter1.value = selectedFilter1.value;
        appliedFilter2.value = selectedFilter2.value;
        appliedFilter3.value = selectedFilter3.value;
        // Apply dynamic filters - copy selected to applied
        appliedFilters.value = Map<String, String?>.from(selectedFilters.value);

        // Store snapshot of current filter options to prevent banner from showing updated options
        _appliedFilterOptionsSnapshot = filterOptions != null
            ? Map<String, List<dynamic>>.from(filterOptions!)
            : {};

        currentPage.value = 1;
        showTooltip.value = true;

        // TODO: Modify callback by passing date range when server side is implemented
        if (onDateFilterChanged != null && filterDate.value != null) {
          onDateFilterChanged!(filterDate.value!);
        }

        // Call multiple date filter callback if present
        if (onDateFilterMultipleChanged != null &&
            selectedDateFilters != null) {
          onDateFilterMultipleChanged!(selectedDateFilters);
        }

        // Call comprehensive filter callback with ALL filters
        if (onAllFiltersChanged != null) {
          Map<String, dynamic> allFilters = {};

          // Add legacy filters if they exist
          if (appliedFilter1.value != null &&
              appliedFilter1.value!.isNotEmpty) {
            allFilters['filter1'] = appliedFilter1.value;
          }
          if (appliedFilter2.value != null &&
              appliedFilter2.value!.isNotEmpty) {
            allFilters['filter2'] = appliedFilter2.value;
          }
          if (appliedFilter3.value != null &&
              appliedFilter3.value!.isNotEmpty) {
            allFilters['filter3'] = appliedFilter3.value;
          }

          // Add dynamic dropdown filters
          appliedFilters.value.forEach((key, value) {
            if (value != null && value.isNotEmpty) {
              allFilters[key] = value;
            }
          });

          // Add date filters
          if (selectedDateFilters != null) {
            selectedDateFilters.forEach((key, value) {
              if (value != null) {
                allFilters[key] = value;
              }
            });
          }

          onAllFiltersChanged!(allFilters);
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.roundIconColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),
      child: Tooltip(
        message: AppStrings.applyFilters,
        child: const Text(applyLabel),
      ),
    );
  }

  Widget _buildDropdown(
    String hint,
    String? value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    // Ensure the value is valid - if it's not in items, set it to null
    String? validValue = value;
    if (validValue != null && !items.contains(validValue)) {
      validValue = null;
    }
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white, // White background for filter dropdown
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: validValue != null
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
          width: 1.0,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: ButtonTheme(
          alignedDropdown: true,
          child: DropdownButton<String>(
            value: validValue,
            hint: Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Text(
                hint,
                style: AppFonts.regularTextStyle(14, color: AppTheme.black),
              ),
            ),
            isExpanded: true,
            dropdownColor: Colors.white, // White background for dropdown menu
            menuMaxHeight: 300,
            borderRadius: BorderRadius.circular(20),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Padding(
                  padding: const EdgeInsets.only(left: 12),
                  child: Text(item, style: AppFonts.regularTextStyle(14)),
                ),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }

  /// Store the position of a date filter for calendar positioning
  void _storeFilterPosition(GlobalKey filterKey, String columnName) {
    final RenderBox? renderBox =
        filterKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final position = renderBox.localToGlobal(Offset.zero);
      _filterPositions[columnName] = position;
      _activeFilterColumn = columnName;
    }
  }

  /// Calculate the position for the calendar popup based on the clicked filter
  Map<String, double?> _calculateCalendarPosition(
    BuildContext context,
    ValueNotifier<bool> showFilter,
  ) {
    final RenderBox? tableBox = context.findRenderObject() as RenderBox?;
    final double containerWidth =
        tableBox?.size.width ?? MediaQuery.of(context).size.width;
    final screenWidth = containerWidth;
    final screenHeight = MediaQuery.of(context).size.height;
    final isMobile = screenWidth < 1000;

    // Responsive width
    final calendarWidth = isMobile
        ? math.min(380.0, screenWidth - 16)
        : math.min(450.0, screenWidth - 48);
    final double maxCalendarWidth = screenWidth - 16;
    final double finalCalendarWidth = calendarWidth > maxCalendarWidth
        ? maxCalendarWidth
        : calendarWidth;

    double? left;
    double? right;
    double top = 0;
    const double verticalSpacing = 6;
    const double calendarHeight = 380;

    if (_activeFilterColumn != null &&
        _filterPositions.containsKey(_activeFilterColumn)) {
      final filterPosition = _filterPositions[_activeFilterColumn]!;

      // Place below filter
      top = filterPosition.dy + filterPosition.dx + verticalSpacing;

      // Flip above if not enough space below
      if (top + calendarHeight > screenHeight - 8) {
        top = filterPosition.dy - calendarHeight - verticalSpacing;
      }

      // Horizontal positioning
      if (isMobile) {
        left = (screenWidth - finalCalendarWidth) / 2;
      } else if (screenWidth <= 1100) {
        left = filterPosition.dx;
        if (left + finalCalendarWidth > screenWidth - 8) {
          left = screenWidth - finalCalendarWidth - 8;
        }
      } else {
        left = filterPosition.dx;
        if (left + finalCalendarWidth > screenWidth - 8) {
          left = screenWidth - finalCalendarWidth - 8;
        }
      }
    } else {
      // Fallback: center
      left = (screenWidth - finalCalendarWidth) / 2;
      top = 150;
    }

    return {
      'left': left,
      'right': right,
      'width': finalCalendarWidth,
      'top': top,
    };
  }

  /// Calculate the top position for the calendar popup
  double _calculateCalendarTop(
    BuildContext context,
    ValueNotifier<bool> showFilter,
  ) {
    // Base position calculation - start from top of widget
    double baseTop = 20; // Small margin from top

    // Add space for title if present
    if (title != null) {
      baseTop += 30; // Title height + spacing
    }

    // Add space for filter toggle button
    baseTop += 20; // Filter button height + spacing

    // If filter is visible, add space for the date filter button specifically
    if (showFilter.value) {
      baseTop +=
          30; // Space for the date filter button row (40px height + 10px spacing)
    }
    if (MediaQuery.of(context).size.width >= 1000) {
      baseTop += 10; // Space for the calendar itself
    }
    // Add space for the calendar itself
    return baseTop;
  }

  Widget _buildDateFilter(
    BuildContext context,
    String hint,
    String? value,
    Function(String?) onChanged,
    ValueNotifier<bool> showCalendar,
    String columnName,
    ValueNotifier<String?> activeDateColumn,
    // Calendar state variables for reset
    ValueNotifier<String> selectedQuickOption,
    ValueNotifier<DateTime?> selectedDate,
    ValueNotifier<DateTime?> rangeStart,
    ValueNotifier<DateTime?> rangeEnd,
    ValueNotifier<bool> isCustomRangeMode,
    ValueNotifier<DateTime?> customRangeStart,
    ValueNotifier<DateTime?> customRangeEnd,
    ValueNotifier<DateTime> currentMonth,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<Map<String, String?>> selectedFilters,
  ) {
    // For now, just use the passed value parameter
    final displayValue = value ?? '';
    // Create a GlobalKey to track this filter's position
    final GlobalKey filterKey = GlobalKey();

    return Container(
      key: filterKey,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white, // White background for date filter
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: displayValue.isNotEmpty
              ? AppTheme.selectedComboBoxBorder
              : AppTheme.comboBoxBorder,
          width: 1.0,
        ),
      ),
      child: Tooltip(
        message: hint,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            borderRadius: BorderRadius.circular(20),
            onTap: () {
              if (showCalendar.value) {
                showCalendar.value = false;
                _activeFilterColumn = null;
              } else {
                // Store the position of this filter for calendar positioning
                _storeFilterPosition(filterKey, columnName);
                // Restore previously applied or selected filter values if they exist
                // Check both appliedFilters (from main Apply button) and selectedFilters (from calendar Apply)
                final appliedValue =
                    appliedFilters.value[columnName] ??
                    selectedFilters.value[columnName];
                if (appliedValue != null && appliedValue.isNotEmpty) {
                  // Parse and restore the applied filter
                  if (appliedValue.contains(' - ')) {
                    // It's a date range
                    final parts = appliedValue.split(' - ');
                    if (parts.length == 2) {
                      final startDate =
                          AppDateFormatter.parseStringToDateMMddyyyy(
                            parts[0].trim(),
                          );
                      final endDate =
                          AppDateFormatter.parseStringToDateMMddyyyy(
                            parts[1].trim(),
                          );

                      if (startDate != null && endDate != null) {
                        isCustomRangeMode.value = true;
                        customRangeStart.value = startDate;
                        customRangeEnd.value = endDate;
                        selectedQuickOption.value = 'Custom Range';
                        currentMonth.value =
                            startDate; // Navigate to the start date's month
                      }
                    }
                  } else {
                    // It's a single date
                    final filterDate =
                        AppDateFormatter.parseStringToDateMMddyyyy(
                          appliedValue,
                        );
                    if (filterDate != null) {
                      //debugPrint('DEBUG: Restoring single date: $filterDate');
                      selectedDate.value = filterDate;
                      isCustomRangeMode.value = false;
                      selectedQuickOption.value = '';
                      currentMonth.value =
                          filterDate; // Navigate to the selected date's month
                    } else {
                      debugPrint(
                        'DEBUG: Failed to parse single date: $appliedValue',
                      );
                    }
                  }
                } else {
                  // No previous filter - reset to defaults
                  selectedQuickOption.value = '';
                  selectedDate.value = null;
                  rangeStart.value = null;
                  rangeEnd.value = null;
                  isCustomRangeMode.value = false;
                  customRangeStart.value = null;
                  customRangeEnd.value = null;
                  currentMonth.value = DateTime.now();
                }

                // Open calendar and set active column
                showCalendar.value = true;
                activeDateColumn.value = columnName;
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      displayValue.isNotEmpty ? displayValue : hint,
                      style: AppFonts.regularTextStyle(
                        14,
                        color: displayValue.isNotEmpty
                            ? AppTheme.black
                            : AppTheme.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: displayValue.isNotEmpty
                        ? AppTheme.selectedComboBoxBorder
                        : AppTheme.black,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showFilter,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<DateTime?> filterDate,
    DateTimeCallback? onDateFilterChanged,
  ) {
    // Check if we have any filters or search functionality
    final hasFilters =
        filterColumnNames.isNotEmpty ||
        filter1Options.isNotEmpty ||
        filter2Options.isNotEmpty ||
        filter3Options.isNotEmpty;

    if (Responsive.isMobile(context)) {
      return _buildMobileHeader(
        context,
        showTooltip,
        appliedF1,
        appliedF2,
        appliedF3,
        appliedFilters,
        selectedF1,
        selectedF2,
        selectedF3,
        selectedFilters,
        filterDate,
        onDateFilterChanged,
        hasFilters,
        showFilter,
        searchQuery,
      );
    }
    // For larger screens (tablet and desktop)
    if (Responsive.isTablet(context)) {
      // Tablet layout: banner on next line
      return _buildTabletHeader(
        hasFilters,
        showFilter,
        context,
        searchQuery,
        showTooltip,
        appliedF1,
        appliedF2,
        appliedF3,
        appliedFilters,
        selectedF1,
        selectedF2,
        selectedF3,
        selectedFilters,
        filterDate,
        onDateFilterChanged,
      );
    }

    // For desktop screens, use the original row layout
    return _buildDesktopHeader(
      showTooltip,
      appliedF1,
      appliedF2,
      appliedF3,
      appliedFilters,
      context,
      selectedF1,
      selectedF2,
      selectedF3,
      selectedFilters,
      filterDate,
      onDateFilterChanged,
      showFilter,
      searchQuery,
    );
  }

  Row _buildDesktopHeader(
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    BuildContext context,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<DateTime?> filterDate,
    DateTimeCallback? onDateFilterChanged,
    ValueNotifier<bool> showFilter,
    ValueNotifier<String> searchQuery,
  ) {
    return Row(
      children: [
        if (titleIcon != null) ...[
          SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
          const SizedBox(width: 8),
        ],
        Text(title ?? '', style: AppFonts.semiBoldTextStyle(22)),
        const SizedBox(width: 12),
        // Show filter results banner inline with title on desktop
        if (showTooltip.value &&
            _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
          _buildFilterResultsBanner(
            context,
            appliedF1,
            appliedF2,
            appliedF3,
            appliedFilters,
            showTooltip,
            filterDate,
            onDateFilterChanged,
            _appliedFilterOptionsSnapshot ?? filterOptions,
          ),
        const Spacer(),
        Tooltip(
          message: showFilter.value
              ? AppStrings.hideFilters
              : AppStrings.showFilters,
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () => showFilter.value = !showFilter.value,
              child: Container(
                constraints: BoxConstraints(
                  minWidth: ResponsiveSizes.filterButtonWidth(context),
                ),
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: showFilter.value
                      ? AppTheme.selectedComboBoxBorder
                      : AppTheme.searchbarBg,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: Image.asset(
                        '$iconAssetpath/filter.png',
                        color: showFilter.value
                            ? Colors.white
                            : AppTheme.tableDataFont,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      filter,
                      style: AppFonts.regularTextStyle(
                        14,
                        color: showFilter.value
                            ? Colors.white
                            : AppTheme.tableDataFont,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          height: 40,
          width: 220,
          child: TextField(
            onChanged: (value) => handleTableSearch != null
                ? handleTableSearch!(value)
                : searchQuery.value = value,
            decoration: InputDecoration(
              hintText: searchHint ?? searchHint,
              hintStyle: AppFonts.regularTextStyle(
                14,
                color: AppTheme.tableDataFont,
              ),
              prefixIcon: Container(
                height: 24,
                width: 24,
                padding: const EdgeInsets.only(left: 8, top: 8, bottom: 8),
                child: Image.asset('$iconAssetpath/search.png'),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(20),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppTheme.searchbarBg,
              contentPadding: const EdgeInsets.symmetric(vertical: 0),
            ),
          ),
        ),
      ],
    );
  }

  Column _buildTabletHeader(
    bool hasFilters,
    ValueNotifier<bool> showFilter,
    BuildContext context,
    ValueNotifier<String> searchQuery,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<DateTime?> filterDate,
    DateTimeCallback? onDateFilterChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (titleIcon != null) ...[
              SizedBox(width: 20, height: 20, child: Image.asset(titleIcon!)),
              const SizedBox(width: 8),
            ],
            Text(title ?? '', style: AppFonts.semiBoldTextStyle(22)),
            const Spacer(),
            if (hasFilters) ...[
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    showFilter.value = !showFilter.value;
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: ResponsiveSizes.filterButtonWidth(context),
                    ),
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          filter,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => handleTableSearch != null
                        ? handleTableSearch!(value)
                        : searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: searchHint ?? searchHint,
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
        // Show filter results banner on next line for tablet
        if (showTooltip.value &&
            _hasAppliedFilters(appliedF1, appliedF2, appliedF3, appliedFilters))
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: _buildFilterResultsBanner(
              context,
              appliedF1,
              appliedF2,
              appliedF3,
              appliedFilters,
              showTooltip,
              filterDate,
              onDateFilterChanged,
              _appliedFilterOptionsSnapshot ?? filterOptions,
            ),
          ),
      ],
    );
  }

  Column _buildMobileHeader(
    BuildContext context,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<DateTime?> filterDate,
    DateTimeCallback? onDateFilterChanged,
    bool hasFilters,
    ValueNotifier<bool> showFilter,
    ValueNotifier<String> searchQuery,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (titleIcon != null) ...[
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: Image.asset(titleIcon!),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Text(title!, style: AppFonts.semiBoldTextStyle(18)),
                  const SizedBox(width: 8),
                  // Show filter results banner inline with title on tablet only (not mobile)
                  if (Responsive.isTablet(context) &&
                      showTooltip.value &&
                      _hasAppliedFilters(
                        appliedF1,
                        appliedF2,
                        appliedF3,
                        appliedFilters,
                      ))
                    Expanded(
                      child: _buildFilterResultsBanner(
                        context,
                        appliedF1,
                        appliedF2,
                        appliedF3,
                        appliedFilters,
                        showTooltip,
                        filterDate,
                        onDateFilterChanged,
                        _appliedFilterOptionsSnapshot ?? filterOptions,
                      ),
                    ),
                  if (!showTooltip.value ||
                      !_hasAppliedFilters(
                        appliedF1,
                        appliedF2,
                        appliedF3,
                        appliedFilters,
                      ) ||
                      Responsive.isMobile(context))
                    const Spacer(),
                ],
              ),
              // Show filter results banner on next line for mobile only
              if (Responsive.isMobile(context) &&
                  showTooltip.value &&
                  _hasAppliedFilters(
                    appliedF1,
                    appliedF2,
                    appliedF3,
                    appliedFilters,
                  ))
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: _buildFilterResultsBanner(
                    context,
                    appliedF1,
                    appliedF2,
                    appliedF3,
                    appliedFilters,
                    showTooltip,
                    filterDate,
                    onDateFilterChanged,
                    _appliedFilterOptionsSnapshot ?? filterOptions,
                  ),
                ),
              // Show filter results banner on next line for tablet
              if (Responsive.isTablet(context) &&
                  showTooltip.value &&
                  _hasAppliedFilters(
                    appliedF1,
                    appliedF2,
                    appliedF3,
                    appliedFilters,
                  ))
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: _buildFilterResultsBanner(
                    context,
                    appliedF1,
                    appliedF2,
                    appliedF3,
                    appliedFilters,
                    showTooltip,
                    filterDate,
                    onDateFilterChanged,
                    _appliedFilterOptionsSnapshot ?? filterOptions,
                  ),
                ),
            ],
          ),
        if (title != null && hasFilters) const SizedBox(height: 12),
        if (hasFilters)
          Row(
            children: [
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    showFilter.value = !showFilter.value;
                    // if (showFilter.value) {
                    //   // When opening filter, restore previously selected values
                    //   // selectedF1.value = appliedF1.value;
                    //   // selectedF2.value = appliedF2.value;
                    //   // selectedF3.value = appliedF3.value;
                    // }
                  },
                  child: Container(
                    constraints: BoxConstraints(
                      minWidth: ResponsiveSizes.filterButtonWidth(context),
                    ),
                    height: 40,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color: showFilter.value
                          ? AppTheme.selectedComboBoxBorder
                          : AppTheme.searchbarBg,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: Image.asset(
                            '$iconAssetpath/filter.png',
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          filter,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: showFilter.value
                                ? Colors.white
                                : AppTheme.tableDataFont,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SizedBox(
                  height: 40,
                  child: TextField(
                    onChanged: (value) => handleTableSearch != null
                        ? handleTableSearch!(value)
                        : searchQuery.value = value,
                    decoration: InputDecoration(
                      hintText: searchHint ?? searchHint,
                      hintStyle: AppFonts.regularTextStyle(
                        14,
                        color: AppTheme.tableDataFont,
                      ),
                      prefixIcon: Container(
                        height: 24,
                        width: 24,
                        padding: const EdgeInsets.only(
                          left: 8,
                          top: 8,
                          bottom: 8,
                        ),
                        child: Image.asset('$iconAssetpath/search.png'),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: AppTheme.searchbarBg,
                      contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    ),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    // Show empty state if no items
    if (items.isEmpty) {
      return _buildEmptyState(context);
    }

    if (Responsive.isMobile(context)) {
      return _buildMobileView(context, items);
    } else if (Responsive.isTablet(context)) {
      return _buildTabletTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    } else {
      return _buildDesktopTable(
        context,
        items,
        handleSort,
        sortColumn,
        sortAscending,
      );
    }
  }

  Widget _buildDesktopTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: actionsColumnHeader,
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
            ),
          );
        }

        // Calculate minimum required width based on number of columns
        final int totalColumns =
            columnNames.length +
            (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth =
            totalColumns * 120.0; // Minimum 120px per column
        final bool enableScroll = constraints.maxWidth < minRequiredWidth;

        Widget tableContent =
            // SizedBox(
            //   width: enableScroll
            //       ? math.max(minRequiredWidth, 1200)
            //       : constraints.maxWidth,
            //   child:
            DataTable(
              columnSpacing: defaultPadding * 0.8,
              dataRowMinHeight: 40,
              dataRowMaxHeight: 48,
              horizontalMargin: 0,
              checkboxHorizontalMargin: 0,
              columns: columns,
              rows: items.map((item) => _buildDataRow(context, item)).toList(),
            );
        // );

        if (enableScroll) {
          return Scrollbar(
            controller: horizontalScrollController,
            thumbVisibility: true,
            child: SingleChildScrollView(
              controller: horizontalScrollController,
              scrollDirection: Axis.horizontal,
              // child: tableContent,
              child: ConstrainedBox(
                constraints: BoxConstraints(minWidth: minRequiredWidth),
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: tableContent,
                ),
              ),
            ),
          );
        } else {
          // return tableContent;
          return SingleChildScrollView(
            scrollDirection: Axis.vertical,
            child: SizedBox(
              width: constraints.maxWidth, // stretch to full width
              child: tableContent,
            ),
          );
        }
      },
    );
  }

  Widget _buildTabletTable(
    BuildContext context,
    List<T> items,
    void Function(String) handleSort,
    ValueNotifier<String> sortColumn,
    ValueNotifier<bool> sortAscending,
  ) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final ScrollController horizontalScrollController = ScrollController();

        final columns = columnNames
            .map(
              (name) => _dataColumn(
                name: name,
                allowSort: true,
                handleSort: handleSort,
                sortColumn: sortColumn.value,
                sortAscending: sortAscending.value,
                fontSize: 12,
              ),
            )
            .toList();

        if (actionBuilders != null && actionBuilders!.isNotEmpty) {
          columns.add(
            _dataColumn(
              name: actionsColumnHeader,
              allowSort: false,
              handleSort: handleSort,
              sortColumn: sortColumn.value,
              sortAscending: sortAscending.value,
              fontSize: 12,
            ),
          );
        }

        // Calculate minimum required width for tablet based on number of columns
        final int totalColumns =
            columnNames.length +
            (actionBuilders != null && actionBuilders!.isNotEmpty ? 1 : 0);
        final double minRequiredWidth =
            totalColumns * 100.0; // Minimum 100px per column for tablet

        return Scrollbar(
          controller: horizontalScrollController,
          thumbVisibility: true, // Always show scrollbar for tablet
          child: SingleChildScrollView(
            controller: horizontalScrollController,
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: math.max(
                minRequiredWidth,
                math.max(constraints.maxWidth, 800),
              ),
              child: Theme(
                data: Theme.of(context).copyWith(
                  dataTableTheme: DataTableThemeData(
                    decoration: const BoxDecoration(
                      color: Colors
                          .transparent, // Remove DataTable's default white background
                    ),
                    dataRowColor: WidgetStateProperty.all(
                      Colors.transparent,
                    ), // Make rows transparent
                    headingRowColor: WidgetStateProperty.all(
                      Colors.transparent,
                    ), // Make header transparent
                  ),
                  cardTheme: const CardThemeData(
                    color: Colors.transparent, // Remove card background
                    elevation: 0,
                  ),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: DataTable(
                  columnSpacing: defaultPadding * 0.6,
                  dataRowMinHeight: 48,
                  dataRowMaxHeight: 52,
                  horizontalMargin: 0,
                  checkboxHorizontalMargin: 0,
                  columns: columns,
                  rows: items
                      .map((item) => _buildDataRow(context, item))
                      .toList(),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  DataColumn _dataColumn({
    required String name,
    bool allowSort = true,
    required void Function(String) handleSort,
    required String sortColumn,
    required bool sortAscending,
    double fontSize = 14,
  }) {
    final bool showSortIcon =
        showSortIconColumns == null || showSortIconColumns!.contains(name);
    return DataColumn(
      label: Builder(
        builder: (context) {
          final processedName = (Responsive.isTablet(context))
              ? name.replaceAll(' ', '\n')
              : name;

          return Expanded(
            child: Tooltip(
              message: processedName,
              child: InkWell(
                mouseCursor: SystemMouseCursors.click,
                hoverColor: Colors.transparent,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                onTap: allowSort && name != actionsColumnHeader
                    ? () => handleSort(name)
                    : null,
                child: Container(
                  alignment:
                      name == actionsColumnHeader && actionBuilders!.length > 1
                      ? Alignment.center
                      : Alignment.centerLeft,
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: IntrinsicWidth(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment:
                          name == actionsColumnHeader &&
                              actionBuilders!.length > 1
                          ? MainAxisAlignment.end
                          : MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Flexible(
                          child: Text(
                            processedName,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign:
                                name == actionsColumnHeader &&
                                    actionBuilders!.length > 1
                                ? TextAlign.end
                                : TextAlign.start,
                            softWrap: true,
                            style: AppFonts.regularTextStyle(
                              fontSize,
                              color: AppTheme.tableHeaderFont,
                            ),
                          ),
                        ),
                        if (allowSort &&
                            name != actionsColumnHeader &&
                            showSortIcon) ...[
                          const SizedBox(width: 4),
                          Image.asset(
                            '$iconAssetpath/column_sort.png',
                            height: 14,
                            width: 14,
                            color: sortColumn == name
                                ? AppTheme.primaryColor
                                : AppTheme.tableHeaderFont,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  DataRow _buildDataRow(BuildContext context, T item) {
    final cells = <DataCell>[];

    for (int i = 0; i < cellBuilders.length; i++) {
      // Check if this column should use icon builder
      final bool useIcon =
          useIconBuilders != null &&
          i < useIconBuilders!.length &&
          useIconBuilders![i] &&
          iconCellBuilders != null &&
          i < iconCellBuilders!.length &&
          iconCellBuilders![i] != null;

      // Check if this column should use widget builder
      final bool useWidget =
          useWidgetBuilders != null &&
          i < useWidgetBuilders!.length &&
          useWidgetBuilders![i] &&
          widgetCellBuilders != null &&
          i < widgetCellBuilders!.length &&
          widgetCellBuilders![i] != null;

      cells.add(
        DataCell(
          Container(
            alignment: Alignment.centerLeft,
            child: useWidget
                ? widgetCellBuilders![i]!(context, item)
                : useIcon
                ? _buildIconCell(iconCellBuilders![i]!(item), context)
                : Text(
                    cellBuilders[i](item),
                    style: AppFonts.regularTextStyle(
                      Responsive.isTablet(context) ? 12 : 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
          ),
        ),
      );
    }

    if (actionBuilders != null && actionBuilders!.isNotEmpty) {
      cells.add(
        DataCell(
          Container(
            width: actionBuilders!.length > 1
                ? ResponsiveSizes.actionColumnWidth(context) +
                      ResponsiveSizes.actionColumnWidth(context) / 1.5
                : ResponsiveSizes.actionColumnWidth(context),
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: actionBuilders!
                  .map((builder) => builder(context, item))
                  .toList(),
            ),
          ),
        ),
      );
    }

    return DataRow(cells: cells);
  }

  Widget _buildIconCell(TableCellData cellData, BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (cellData.leftIcon != null)
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Icon(
              cellData.leftIcon,
              size: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        if (cellData.leftIconAsset != null)
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: Image.asset(
              cellData.leftIconAsset!,
              width: cellData.iconSize,
              height: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        Flexible(
          child: Text(
            cellData.text,
            style: AppFonts.regularTextStyle(
              Responsive.isTablet(context) ? 12 : 14,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
            textAlign: cellData.alignment ?? TextAlign.start,
          ),
        ),
        if (cellData.rightIcon != null)
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Icon(
              cellData.rightIcon,
              size: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
        if (cellData.rightIconAsset != null)
          Padding(
            padding: const EdgeInsets.only(left: 8),
            child: Image.asset(
              cellData.rightIconAsset!,
              width: cellData.iconSize,
              height: cellData.iconSize,
              color: cellData.iconColor,
            ),
          ),
      ],
    );
  }

  Widget _buildMobileView(BuildContext context, List<T> items) {
    if (mobileCardBuilder == null) {
      return const SizedBox();
    }

    return Column(
      children: items.map((item) => mobileCardBuilder!(context, item)).toList(),
    );
  }

  Widget _buildPagination(
    BuildContext context,
    List<T> filteredItems,
    ValueNotifier<int> currentPage,
    ValueNotifier<int> itemsPerPage,
    IntCallback? handlePagination,
  ) {
    // Don't show pagination if no data
    if (filteredItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalPages = pageCount;

    // Always show pagination info, even with 1 page
    if (Responsive.isMobile(context)) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _showingDataText(
            context,
            currentPage.value,
            itemsPerPage.value,
            filteredItems,
          ),
          if (totalPages > 1) ...[
            const SizedBox(height: defaultPadding),
            _buildPaginationControls(
              context,
              currentPage,
              totalPages,
              handlePagination,
            ),
          ],
        ],
      );
    }

    return Row(
      children: [
        _showingDataText(
          context,
          currentPage.value,
          itemsPerPage.value,
          filteredItems,
        ),
        if (totalPages > 0) ...[
          const Spacer(),
          _buildPaginationControls(
            context,
            currentPage,
            totalPages,
            handlePagination,
          ),
        ],
      ],
    );
  }

  Widget _showingDataText(
    BuildContext context,
    int currentPage,
    int itemsPerPage,
    List<T> filteredItems,
  ) {
    final startIndex = (currentPage - 1) * itemsPerPage + 1;
    final endIndex = (currentPage * itemsPerPage).clamp(0, totalElements);

    return Text(
      'Showing $startIndex to $endIndex of $totalElements entries',
      style: AppFonts.regularTextStyle(14, color: AppTheme.tableDataFont),
    );
  }

  Widget _buildPaginationControls(
    BuildContext context,
    ValueNotifier<int> currentPage,
    int totalPages,
    IntCallback? handlePagination,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Tooltip(
          message: AppStrings.previous,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            child: _paginationButton(
              icon: Icons.chevron_left,
              onPressed: currentPage.value > 1
                  ? () => handlePagination != null
                        ? {
                            currentPage.value--,
                            handlePagination(currentPage.value),
                          }
                        : currentPage.value--
                  : null,
            ),
          ),
        ),
        ...List.generate(math.min(5, totalPages), (index) {
          final pageNum = index + 1;
          return _paginationButton(
            label: pageNum.toString(),
            isSelected: pageNum == currentPage.value,
            onPressed: () => handlePagination != null
                ? {currentPage.value = pageNum, handlePagination(pageNum)}
                : currentPage.value = pageNum,
          );
        }),
        if (totalPages > 5) ...[
          _paginationButton(label: '...'),
          _paginationButton(
            label: totalPages.toString(),
            onPressed: () => handlePagination != null
                ? {currentPage.value = totalPages, handlePagination(totalPages)}
                : currentPage.value = totalPages,
          ),
        ],
        Tooltip(
          message: AppStrings.next,
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            child: _paginationButton(
              icon: Icons.chevron_right,
              onPressed: currentPage.value < totalPages
                  ? () => handlePagination != null
                        ? {
                            currentPage.value++,
                            handlePagination(currentPage.value),
                          }
                        : currentPage.value++
                  : null,
            ),
          ),
        ),
      ],
    );
  }

  Widget _paginationButton({
    String? label,
    IconData? icon,
    bool isSelected = false,
    VoidCallback? onPressed,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: Tooltip(
        message: label ?? '',
        child: Material(
          color: isSelected ? AppTheme.paginationActiveBg : Colors.transparent,
          borderRadius: BorderRadius.circular(5),
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            onTap: onPressed,
            borderRadius: BorderRadius.circular(5),
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(
                  color: isSelected ? Colors.transparent : Colors.grey.shade300,
                ),
              ),
              child: Center(
                child: icon != null
                    ? Icon(
                        icon,
                        size: 16,
                        color: isSelected ? Colors.white : Colors.black,
                      )
                    : Text(
                        label!,
                        style: AppFonts.regularTextStyle(
                          12,
                          color: isSelected ? Colors.white : Colors.black,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(defaultPadding * 3),
      child: Center(
        child: Text(
          emptyStateMessage ?? noDataAvailable,
          style: AppFonts.regularTextStyle(
            Responsive.isMobile(context) ? 16 : 18,
            color: AppTheme.primaryTextColor.withValues(alpha: 0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // Helper method to generate dynamic filter options
  List<String> _getDynamicFilterOptions(String columnName) {
    if (filterValueExtractors == null ||
        !filterValueExtractors!.containsKey(columnName)) {
      return [];
    }

    final extractor = filterValueExtractors![columnName]!;
    final uniqueValues = data.map(extractor).toSet().toList();
    uniqueValues.sort(); // Sort alphabetically
    return uniqueValues;
  }

  // Helper method to get filter label (use column name if not specified)
  String _getFilterLabel(String columnName) {
    return columnName; // You can customize this logic if needed
  }

  // Helper method to check if any filters are applied
  bool _hasAppliedFilters(
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
  ) {
    // Check legacy filters
    if (appliedF1.value != null && appliedF1.value!.isNotEmpty) return true;
    if (appliedF2.value != null && appliedF2.value!.isNotEmpty) return true;
    if (appliedF3.value != null && appliedF3.value!.isNotEmpty) return true;

    // Check dynamic filters
    for (String? value in appliedFilters.value.values) {
      if (value != null && value.isNotEmpty) return true;
    }

    return false;
  }

  _clearAllAppliedSelectedFilters(
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<DateTime?> filterDate,
    ValueNotifier<DateTime?> selectedDate,
    ValueNotifier<String?> selectedF1,
    ValueNotifier<String?> selectedF2,
    ValueNotifier<String?> selectedF3,
    ValueNotifier<Map<String, String?>> selectedFilters,
    ValueNotifier<Map<String, DateTime?>> selectedDateFilters,
  ) {
    appliedF1.value = null;
    appliedF2.value = null;
    appliedF3.value = null;
    appliedFilters.value = {};
    filterDate.value = null;
    selectedDate.value = null;
    selectedF1.value = null;
    selectedF2.value = null;
    selectedF3.value = null;
    selectedDateFilters.value = {};
    selectedFilters.value = {};
  }

  // Build filter results banner widget
  Widget _buildFilterResultsBanner(
    BuildContext context,
    ValueNotifier<String?> appliedF1,
    ValueNotifier<String?> appliedF2,
    ValueNotifier<String?> appliedF3,
    ValueNotifier<Map<String, String?>> appliedFilters,
    ValueNotifier<bool> showTooltip,
    ValueNotifier<DateTime?> filterDate,
    DateTimeCallback? onDateFilterChanged,
    Map<String, List<dynamic>>? filterOptions,
  ) {
    // Build the filter text using only applied filter values
    List<String> filterTexts = [];

    // Add legacy filter values
    if (appliedF1.value != null && appliedF1.value!.isNotEmpty) {
      filterTexts.add(appliedF1.value!);
    }
    if (appliedF2.value != null && appliedF2.value!.isNotEmpty) {
      filterTexts.add(appliedF2.value!);
    }
    if (appliedF3.value != null && appliedF3.value!.isNotEmpty) {
      filterTexts.add(appliedF3.value!);
    }

    // Add dynamic filter values (show display value, not ID)
    appliedFilters.value.forEach((key, id) {
      if (id != null && id.isNotEmpty) {
        final options = filterOptions != null && filterOptions[key] != null
            ? filterOptions[key]!
            : null;
        if (options != null && options.isNotEmpty) {
          final match = options.where((f) => f.id == id).toList();
          if (match.isNotEmpty) filterTexts.add((match.first.value ?? id));
        } else {
          if (key.contains('Date')) {
            filterTexts.add(id);
          }
        }
      }
    });

    final String filterText = filterTexts.join(', ');

    return Container(
      height: 25,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: AppTheme.filterResultBannerColor,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(
          color: AppTheme.filterResultBannerBorderColor,
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          RichText(
            text: TextSpan(
              text: filterResultBannerText,
              style: AppFonts.regularTextStyle(11, color: AppTheme.black),
              children: [
                TextSpan(
                  text: filterText,
                  style: AppFonts.semiBoldTextStyle(11, color: AppTheme.black),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 10,
            child: Tooltip(
              message: AppStrings.resetFilters,
              child: IconButton(
                padding: EdgeInsets.zero,
                alignment: Alignment.center,
                icon: const Icon(Icons.close, size: 16),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.transparent,
                ),
                onPressed: () {
                  // Clear all applied filters
                  appliedF1.value = null;
                  appliedF2.value = null;
                  appliedF3.value = null;
                  appliedFilters.value = {};
                  filterDate.value = null;
                  if (onDateFilterMultipleChanged != null) {
                    onDateFilterMultipleChanged!({});
                  }
                  if (onDateFilterChanged != null) {
                    onDateFilterChanged(filterDate.value);
                  }
                  if (onAllFiltersChanged != null) {
                    onAllFiltersChanged!({});
                  }

                  // Hide the banner
                  showTooltip.value = false;
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Applies date filtering to the data based on the selected date range
  List<T> _applyDateFilter(
    List<T> items,
    String Function(T) extractor,
    String appliedValue,
  ) {
    // Parse the applied value to determine if it's a single date or date range
    if (appliedValue.contains(' - ')) {
      // It's a date range
      final parts = appliedValue.split(' - ');
      if (parts.length == 2) {
        final startDate = AppDateFormatter.parseStringToDateMMddyyyy(
          parts[0].trim(),
        );
        final endDate = AppDateFormatter.parseStringToDateMMddyyyy(
          parts[1].trim(),
        );

        if (startDate != null && endDate != null) {
          return items.where((item) {
            final itemDateString = extractor(item);
            final itemDate = DateRangeUtils.parseDate(itemDateString);

            if (itemDate != null) {
              return DateRangeUtils.isDateInRange(itemDate, startDate, endDate);
            }
            return false;
          }).toList();
        }
      }
    } else {
      // It's a single date
      final filterDate = AppDateFormatter.parseStringToDateMMddyyyy(
        appliedValue,
      );
      if (filterDate != null) {
        return items.where((item) {
          final itemDateString = extractor(item);
          final itemDate = DateRangeUtils.parseDate(itemDateString);

          if (itemDate != null) {
            return DateRangeUtils.isSameDay(itemDate, filterDate);
          }
          return false;
        }).toList();
      }
    }

    // If parsing fails, return original items
    return items;
  }
}
